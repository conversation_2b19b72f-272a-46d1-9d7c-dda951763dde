{"name": "deepinsigths", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "react-router build", "coverage": "vitest --coverage --passWithNoTests --run", "predev": "path-exists ./.env.development.secrets || (echo 'Missing secrets file .env.development.secrets; get from https://go/frontendsecretsfile.' && false)", "dev": "react-router dev", "format:check": "prettier \"**/*.{j(s|sx|son),(m|c)js,t(s|sx)}\" --list-different", "format:fix": "prettier \"**/*.{j(s|sx|son),(m|c)js,t(s|sx)}\" --write --log-level=warn", "format:staged": "prettier $(git diff --cached --name-only --diff-filter=ACMR | sed 's| |\\ |g') --write --ignore-unknown", "lint": "eslint . --ext=js,mjs,cjs,jsx,ts,tsx", "prepare": "# ./.vscode/vscode-npm-prepare-hook.sh && husky install", "regenerate-api": "./regenerate_apis.sh", "serve": "node --max-http-header-size 32678 --experimental-require-module --require ./instrumentation.server.js server.js", "test": "vitest --passWithNoTests --run", "test:watch": "vitest --coverage --passWithNoTests", "typecheck": "react-router typegen && tsc", "check": "VITE_CJS_TRACE=true vite dev"}, "dependencies": {"@datadog/browser-logs": "^6.12.0", "@datadog/browser-rum": "^6.12.0", "@datadog/browser-worker": "^6.12.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@epic-web/client-hints": "^1.3.5", "@fontsource-variable/inter": "^5.1.1", "@jsonforms/core": "^3.5.1", "@jsonforms/react": "^3.5.1", "@jsonforms/vanilla-renderers": "^3.5.1", "@mdxeditor/editor": "^3.29.1", "@nozbe/microfuzz": "^1.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.57.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.200.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.200.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-node": "^0.200.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-router/express": "7.6.2", "@react-router/fs-routes": "^7.6.2", "@react-router/node": "7.6.2", "@tailwindcss/typography": "^0.5.16", "chart.js": "^4.4.7", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "compression": "^1.7.4", "console-stamp": "^3.1.2", "date-fns": "^2.30.0", "dayjs": "^1.11.9", "deep-object-diff": "^1.1.9", "dotenv": "^16.3.1", "express": "^4.21.2", "input-otp": "^1.2.4", "intro.js-react": "^1.0.0", "isbot": "^4.4.0", "js-cookie": "^3.0.5", "jsonschema": "^1.5.0", "libphonenumber-js": "^1.11.17", "lodash.debounce": "^4.0.8", "lucide-react": "^0.503.0", "marked": "^15.0.12", "morgan": "^1.10.0", "opentelemetry-instrumentation-remix": "^0.8.0", "path-exists-cli": "^2.0.0", "react": "^18.3.0", "react-audio-voice-recorder": "^2.2.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.0", "react-dom": "^18.3.0", "react-grid-layout": "^1.5.0", "react-markdown": "^9.0.1", "react-phone-number-input": "^3.4.10", "react-router": "7.6.2", "react-screen-wake-lock": "^3.0.2", "react-toastify": "^9.0.3", "react-transition-group": "^4.4.5", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "remix-auth": "^3.6.0", "remix-auth-form": "^1.4.0", "remix-auth-microsoft": "^2.0.1", "remix-auth-oauth2": "^1.11.1", "remix-themes": "^1.3.1", "remix-utils": "^7.6.0", "source-map-support": "^0.5.21", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.18.3", "@react-router/dev": "7.6.2", "@testing-library/jest-dom": "^6.6.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.6.1", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/intercom-web": "^2.8.26", "@types/lodash.debounce": "^4.0.9", "@types/morgan": "^1.9.9", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-grid-layout": "^1.3.5", "@types/react-window": "^1.8.8", "@types/source-map-support": "^0.5.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.34.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.0.0", "autoprefixer": "^10.4.16", "chokidar": "^3.5.3", "eslint": "^9.28.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "8.0.3", "jest": "^29.7.0", "jsdom": "^25.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "0.4.1", "pretty-quick": "3.1.3", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^5.4.14", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.0"}, "volta": {"node": "20.18.1", "npm": "10.2.4"}}