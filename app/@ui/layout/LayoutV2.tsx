import { TopNavBar } from "./TopNavBar";
import { Link, NavLink } from "react-router";
import { type VariantProps, cva } from "class-variance-authority";
import {
  ChartNoAxesCombined,
  HelpCircle,
  House,
  ListChecks,
  LogOut,
  NotepadText,
  Settings,
  UserIcon,
  Users,
} from "lucide-react";
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Avatar, AvatarFallback } from "~/@shadcn/ui/avatar";
import { Button } from "~/@shadcn/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/@shadcn/ui/dropdown-menu";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { cn } from "~/@shadcn/utils";
import { Typography } from "~/@ui/Typography";
import { useFlag } from "~/context/flags";
import {
  INTERCOM_LAUNCHER_SELECTOR,
  useIntercom,
} from "~/third-party/Intercom/Intercom";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";
import OnboardingMenu from "./components/OnboardingMenu";

// Contexts
export type LayoutContextValue = {
  /**
   * Setting `favorSidebarOnMobile` to `true` will cause the `<Sidebar />` panel
   * to appear in place of (or, more accurately, "on top of") the `<Content />`
   * panel on mobile.
   *
   * This allows to use Remix's `<Outlet />`s to render child route content in a
   * sidebar, next to parent route content, on desktop, while having child route
   * content take up the whole screen on mobile.
   *
   * Child routes have to explicitly opt-in to displaying their sidebars over
   * parent content (aka "favoring" sidebar over content) by calling the
   * `setFavorSidebarOnMobile(true)` method.
   *
   * The `favorSidebarOnMobile` flag has no effect on desktop and tablet
   * resolutions.
   */
  favorSidebarOnMobile: boolean;
  setFavorSidebarOnMobile: React.Dispatch<React.SetStateAction<boolean>>;
};
export const LayoutContext = createContext<LayoutContextValue>({
  favorSidebarOnMobile: false,
  setFavorSidebarOnMobile: () => {
    throw Error(
      "Missing React context. Call to setFavorSidebarOnMobile in a component that is not wrapped the Layout component."
    );
  },
});

// Exports
export const PageTitleInput = (
  props: React.ComponentProps<typeof TextareaGrowable>
) => (
  <TextareaGrowable
    defaultValue="Discussion with stuff"
    variant="default"
    typographyVariant="h1"
    {...props}
  />
);

type PageTitleTextProps = Omit<
  React.ComponentProps<typeof Typography>,
  "variant"
>;
export const PageTitleText = ({ className, ...props }: PageTitleTextProps) => (
  <Typography
    className={cn(
      "inline-flex grow justify-start self-stretch py-2",
      className
    )}
    variant="h1"
    {...props}
  />
);

type HeaderV2Props = Omit<React.ComponentProps<"header">, "children"> & {
  leftClassName?: string;
  rightClassName?: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  subtitle?: string;
};
export const HeaderV2 = ({
  className,
  leftClassName,
  rightClassName,
  left,
  right,
  subtitle,
  ...props
}: HeaderV2Props) => (
  <header
    id="LayoutHeader"
    className={cn(
      "z-10 flex h-10 w-full shrink-0 items-center gap-3 bg-background md:px-5 md:pl-2",
      className
    )}
    {...props}
  >
    {/* Left slot */}
    {left && (
      <div
        className={cn(
          "flex shrink-0 gap-3",
          subtitle ? "grow-0" : "grow",
          leftClassName
        )}
      >
        {left}
      </div>
    )}

    {/* Subtitle slot */}
    {subtitle && (
      <Typography
        className="grow overflow-hidden text-ellipsis whitespace-nowrap text-lg"
        color="secondary"
      >
        {subtitle}
      </Typography>
    )}
    {/* Right slot */}
    {right && (
      <div className={cn("flex shrink-0 gap-1", rightClassName)}>{right}</div>
    )}
  </header>
);

const UserMenu = ({
  isIntercomAvailable,
  className,
  side,
  align,
}: {
  isIntercomAvailable: boolean;
  className?: string;
  side: "right" | "bottom";
  align: "start" | "center" | "end";
}) => (
  <div className={className} data-onboarding="usermenu">
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-12 w-12">
          <Tooltip>
            <TooltipTrigger asChild>
              <Avatar className="h-9 w-9">
                <AvatarFallback>
                  <UserIcon className="!h-5 !w-5" />
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent side="right">User menu</TooltipContent>
          </Tooltip>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side={side}
        align={align}
        hideWhenDetached={true}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <DropdownMenuItem asChild>
          <Button variant="ghost" asChild className="w-full justify-start">
            <Link to="/settings">
              <Settings className="!h-5 !w-5" />
              <span>Settings</span>
            </Link>
          </Button>
        </DropdownMenuItem>
        {isIntercomAvailable && (
          <DropdownMenuItem asChild>
            <Button
              id={INTERCOM_LAUNCHER_SELECTOR}
              variant="ghost"
              className="w-full justify-start"
            >
              <HelpCircle className="!h-5 !w-5" />
              <span>Get help</span>
            </Button>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem asChild>
          <Button
            variant="ghost-destructive"
            asChild
            className="w-full justify-start"
          >
            <Link to="/auth/logout">
              <LogOut className="!h-5 !w-5" />
              <span>Logout</span>
            </Link>
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
);

const navBarVariants = cva("shrink-0", {
  variants: {
    variant: {
      desktop:
        "hidden h-full w-[70px] flex-col items-start justify-between px-2 py-3 md:flex",
      mobile:
        "fixed bottom-0 left-0 right-0 z-10 flex flex-row items-center self-stretch border-t border-border bg-background shadow-lg md:hidden",
    },
  },
  defaultVariants: { variant: "desktop" },
});

const navBarInnerVariants = cva("flex", {
  variants: {
    variant: {
      desktop:
        "w-full shrink-0 flex-col items-center justify-start gap-2 rounded-lg bg-muted px-1 py-2",
      mobile: "h-full w-full flex-grow items-center justify-around",
    },
  },
  defaultVariants: { variant: "desktop" },
});

const navLinkVariants = cva(
  "inline-flex shrink-0 select-none items-center justify-center px-3 hover:bg-accent [&.active]:bg-warning-foreground/30 [&.active]:text-warning",
  {
    variants: {
      variant: {
        desktop: "h-12 w-12 rounded-xl",
        mobile:
          "h-12 w-1/4 flex-col [&.active]:shadow-[0_0.25rem_0_0_hsl(var(--warning))_inset]",
      },
    },
    defaultVariants: { variant: "desktop" },
  }
);

type NavBarV2Props = Omit<React.ComponentProps<"nav">, "children"> &
  VariantProps<typeof navBarVariants> & {
    innerClassName?: boolean;
    isIntercomAvailable: boolean;
  };
export const NavBarV2 = ({
  className,
  innerClassName,
  variant,
  isIntercomAvailable,
  ...props
}: NavBarV2Props) => {
  const { matchedBreakpoints } = useTailwindBreakpoints();
  const isClientViewEnabled = useFlag("EnableClientView");
  const isAdvisorHubEnabled = useFlag("EnableDashboardFeature");
  const isInsightsDashboardEnabled = useFlag("EnablePracticeInsightsDashboard");
  const isOnboardingEnabled = useFlag("EnableInAppTutorials");

  const tooltipSide = matchedBreakpoints.has("md") ? "right" : "top";
  return (
    <nav
      id="LayoutNavBar"
      className={cn(navBarVariants({ variant }), className)}
      {...props}
    >
      <div
        id="LayoutNavBar-upper"
        className={cn(navBarInnerVariants({ variant }), innerClassName)}
      >
        {isAdvisorHubEnabled && (
          <Tooltip>
            <TooltipTrigger asChild>
              <NavLink to="/dashboard" className={navLinkVariants({ variant })}>
                <House />
                <span className="text-xs md:hidden">Hub</span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side={tooltipSide}>Advisor Hub</TooltipContent>
          </Tooltip>
        )}
        <Tooltip>
          <TooltipTrigger asChild>
            <NavLink to="/notes" className={navLinkVariants({ variant })}>
              <NotepadText />
              <span className="text-xs md:hidden">Notes</span>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side={tooltipSide}>Notes</TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <NavLink to="/tasks" className={navLinkVariants({ variant })}>
              <ListChecks />
              <span className="text-xs md:hidden">Tasks</span>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side={tooltipSide}>Tasks</TooltipContent>
        </Tooltip>
        {isClientViewEnabled && (
          <Tooltip>
            <TooltipTrigger asChild>
              <NavLink to="/clients" className={navLinkVariants({ variant })}>
                <Users />
                <span className="text-xs md:hidden">Clients</span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side={tooltipSide}>Clients</TooltipContent>
          </Tooltip>
        )}
        {isInsightsDashboardEnabled && (
          <Tooltip>
            <TooltipTrigger asChild>
              <NavLink to="/insights" className={navLinkVariants({ variant })}>
                <ChartNoAxesCombined />
                <span className="text-xs md:hidden">Insights</span>
              </NavLink>
            </TooltipTrigger>
            <TooltipContent side={tooltipSide}>Insights</TooltipContent>
          </Tooltip>
        )}
      </div>
      <div
        id="LayoutNavBar-lower"
        className="mb-4 mt-8 hidden w-full shrink-0 flex-col items-center justify-center gap-2 md:flex"
      >
        {isOnboardingEnabled && <OnboardingMenu />}
        <UserMenu
          isIntercomAvailable={isIntercomAvailable}
          side="right"
          align="end"
          data-onboarding="usermenu"
        />
      </div>
    </nav>
  );
};

type SidebarV2Props = React.ComponentProps<"aside"> & {
  favorSidebarOnMobile?: boolean;
  header?: ReactNode;
  innerClassName?: string;
};
export const SidebarV2 = ({
  children,
  className,
  favorSidebarOnMobile: initialFavorSidebarOnMobile = false,
  header,
  innerClassName,
  ...props
}: SidebarV2Props) => {
  const { favorSidebarOnMobile, setFavorSidebarOnMobile } =
    useContext(LayoutContext);
  useEffect(() => {
    setFavorSidebarOnMobile(initialFavorSidebarOnMobile);
  }, [initialFavorSidebarOnMobile, setFavorSidebarOnMobile]);

  return (
    <aside
      id="LayoutSidebar"
      className={cn(
        "h-full w-full flex-grow flex-col items-center self-stretch overflow-y-scroll p-4",
        favorSidebarOnMobile ? "flex lg:flex" : "hidden lg:flex",
        className
      )}
      {...props}
    >
      {header}
      <div
        id="LayoutSidebar-inner"
        className={cn(
          "flex w-full flex-col items-start gap-3 pb-4 lg:max-w-full",
          innerClassName
        )}
      >
        {children}
      </div>
      <div id="footer">
        <div className="h-20" />
      </div>
    </aside>
  );
};

type ContentV2Props = React.ComponentProps<"main"> & {
  floatingAction?: ReactNode;
  footerClassName?: string;
  header?: ReactNode;
  innerClassName?: string;
};

export const ContentV2 = ({
  children,
  className,
  floatingAction,
  footerClassName,
  header,
  innerClassName,
  ...props
}: ContentV2Props) => {
  const { favorSidebarOnMobile } = useContext(LayoutContext);
  return (
    <main
      id="LayoutContent"
      className={cn(
        "flex h-full w-full flex-grow flex-col items-center self-stretch overflow-hidden",
        favorSidebarOnMobile ? "hidden lg:flex" : "flex lg:flex",
        className
      )}
      {...props}
    >
      {header}
      <div
        id="LayoutContent-inner"
        className={cn(
          "flex min-h-0 w-full flex-grow flex-col items-start gap-3 overflow-y-auto p-4",
          innerClassName
        )}
      >
        {children}
      </div>
      {floatingAction && (
        <div
          id="LayoutContent-floatingAction"
          className="fixed bottom-[6rem] right-5 flex flex-col md:hidden"
        >
          {floatingAction}
        </div>
      )}
    </main>
  );
};

type LayoutV2Props = React.ComponentProps<"div"> & {
  favorSidebarOnMobile?: boolean;
  header?: ReactNode;
};
export const LayoutV2 = ({
  children,
  className,
  favorSidebarOnMobile: initialFavorSidebarOnMobile = false,
  ...props
}: LayoutV2Props) => {
  const [favorSidebarOnMobile, setFavorSidebarOnMobile] = useState(
    initialFavorSidebarOnMobile
  );
  const contextValue = useMemo(
    () => ({ favorSidebarOnMobile, setFavorSidebarOnMobile }),
    [favorSidebarOnMobile, setFavorSidebarOnMobile]
  );
  const { isIntercomAvailable } = useIntercom();

  return (
    <LayoutContext.Provider value={contextValue}>
      <div
        id="Layout"
        className={cn(
          "flex h-full w-full flex-col self-stretch overflow-hidden md:flex-row",
          className
        )}
        {...props}
      >
        {/* NavBar desktop (hidden on mobile resolutions) */}
        <NavBarV2 variant="desktop" isIntercomAvailable={isIntercomAvailable} />

        <div className="flex h-full min-h-0 min-w-0 flex-1 flex-col">
          {/* Top NavBar */}
          <TopNavBar
            rightChild={
              <UserMenu
                isIntercomAvailable={isIntercomAvailable}
                className={"md:hidden"}
                side="bottom"
                align="end"
              />
            }
          />

          {/* Content and Sidebar are passed as children */}
          <div className="flex h-full min-h-0 w-full min-w-0 flex-grow items-center self-stretch">
            {children}
          </div>
        </div>

        {/* NavBar mobile (hidden on desktop resolutions) */}
        <NavBarV2 variant="mobile" isIntercomAvailable={isIntercomAvailable} />
      </div>
    </LayoutContext.Provider>
  );
};
