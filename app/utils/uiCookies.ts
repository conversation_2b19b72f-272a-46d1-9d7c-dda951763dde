import getCookieValue from "./getCookieValue";

export function getTheme(cookieHeader: string | null) {
  if (!cookieHeader) {
    return "light";
  }

  let theme = "";

  try {
    const uiSettings = JSON.parse(
      decodeURIComponent(getCookieValue(cookieHeader, "ui-settings") || "")
    );

    theme = uiSettings.theme;
  } catch (e) {
    theme = "light";
  }

  return theme;
}

// save settings in cookie (expires in 365 days)
export function saveUiSettingsInCookies(settings: { theme: string }) {
  document.cookie = `ui-settings=${encodeURIComponent(
    JSON.stringify(settings)
  )}; path=/; max-age=${60 * 60 * 24 * 365}`;
}
