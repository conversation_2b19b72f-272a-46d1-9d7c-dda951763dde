import { ChevronDown, ChevronRight, RefreshCcw } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "~/@shadcn/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { Switch } from "~/@shadcn/ui/switch";
import { cn } from "~/@shadcn/utils";

type DataType = {
  Icon: React.ElementType;
  name: string;
  key: string;
  selectionContent: string | React.ReactNode;
  isReadOnly?: boolean;
};

type SelectionModalProps = {
  dataSet?: DataType[];
  onClose: () => void;
  onContinue: (data: Record<string, boolean>) => void;
};

type SelectionDataType = { [key: string]: boolean };

const SelectionModal = ({
  dataSet,
  onClose,
  onContinue,
}: SelectionModalProps) => {
  const [selectionData, setSelectionData] = useState<SelectionDataType>({});
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    if (dataSet) {
      setSelectionData(
        dataSet.reduce(
          (acc, curr) => {
            // don't add read-only items to the selection data; else API will fail
            if (!curr.isReadOnly) {
              acc[curr.key] = true;
            }
            return acc;
          },
          {} as Record<string, boolean>
        )
      );

      setHasLoaded(true);
    }
  }, [dataSet]);

  const updateSelectionData = (key: keyof typeof selectionData) => {
    setSelectionData((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // disable CTA if no section is selected
  const shouldDisableSubmission = !Object.values(selectionData).some((_) => _);

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-5xl p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle>Attributes to sync</DialogTitle>
          <DialogDescription>
            {hasLoaded
              ? "Select the data you want to sync to your CRM. Once this note is synced, you will not be able to edit it on Zeplyn."
              : "Please wait while we load the details for you.."}
          </DialogDescription>
        </DialogHeader>

        <div className="my-2 flex max-h-[60vh] min-h-[40vh] flex-col gap-2 overflow-auto md:grid md:min-h-[50vh] md:grid-cols-2 md:content-start md:items-start">
          {hasLoaded &&
            dataSet?.map((item) => (
              <AttributeCell
                item={item}
                key={item.key}
                isSelected={selectionData[item.key]}
                onCheckedChange={() => updateSelectionData(item.key)}
              />
            ))}
        </div>

        <DialogFooter>
          <div className="w-full">
            {hasLoaded && (
              <DialogDescription>
                By clicking <strong>Synchronize</strong>, you certify that the
                interaction record you are syncing is accurate.
              </DialogDescription>
            )}

            <div className="mt-2 flex items-center justify-end gap-2">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                disabled={!hasLoaded || shouldDisableSubmission}
                onClick={() => onContinue(selectionData)}
              >
                <RefreshCcw />
                Synchronize
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const AttributeCell = ({
  item: { Icon, key, name, selectionContent, isReadOnly },
  isSelected,
  onCheckedChange,
}: {
  item: DataType;
  isSelected?: boolean;
  onCheckedChange: () => void;
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const ToggleIcon = isCollapsed ? ChevronRight : ChevronDown;

  return (
    <div
      key={key}
      className={cn(
        "rounded-sm border border-muted bg-muted p-4",
        (isSelected || isReadOnly) && "border-success",
        !isCollapsed && "md:self-stretch"
      )}
      onClick={() => setIsCollapsed(!isCollapsed)}
    >
      <div className="flex justify-between">
        <span className="flex items-center gap-2 font-semibold">
          <Icon size={18} className="text-muted-foreground" />
          {name}
        </span>
        <Switch
          disabled={isReadOnly}
          checked={isReadOnly ? true : isSelected}
          onCheckedChange={onCheckedChange}
          onClick={(e) => {
            e.stopPropagation();
          }}
          title={isReadOnly ? "This attribute will always be synced" : ""}
          className="ml-auto"
        />
        <ToggleIcon className="ml-2 cursor-pointer" />
      </div>
      {!isCollapsed && (
        <div className="mt-2 text-sm text-muted-foreground">
          {selectionContent}
        </div>
      )}
    </div>
  );
};

export default SelectionModal;
