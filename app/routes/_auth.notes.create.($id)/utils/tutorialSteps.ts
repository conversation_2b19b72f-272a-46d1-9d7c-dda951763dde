const tutorialSteps = (enableSaveScheduledNotes: boolean) => [
  {
    title: "This is the Note Page",
    intro:
      "Here, you can review, edit, sync, or delete meeting notes. This is also where you can start an in-person meeting.",
  },
  {
    title: "Meeting Actions",
    element: "#LayoutHeader",
    intro:
      "Return to the hub by clicking the back arrow. Click on the Edit button to edit the note fields, and Save when done.",
  },
  ...(enableSaveScheduledNotes
    ? [
        {
          title: "Meeting Date and Time",
          element: "[data-onboarding='scheduled-timestamp']",
          intro:
            "As long as the meeting is pending you can update the date and start time.",
        },
      ]
    : []),
  {
    title: "Meeting Tabs",
    element: "[data-onboarding='meeting-tabs']",
    intro:
      "These tabs will be your primary navigation on the Note page. Before a meeting you’ll only see Meeting Details and Meeting Prep; after the meeting, new tabs will appear.",
  },
  {
    title: "Meeting Type",
    element: "#meetingType",
    intro:
      "This is the meeting type dropdown. Meetings default to <PERSON><PERSON> but you can choose any meeting type from this list. A meeting type is required to generate Meeting Prep.",
  },
  {
    title: "Who’s attending?",
    element: "#attendees",
    intro:
      "When your calendar is integrated, everyone you invite will show up here. You can also add new people by choosing them from the drop down, typing their name, or adding their email address.",
  },
  {
    title: "How will Zeplyn join the meeting?",
    element: "[data-onboarding='audio-source']",
    intro:
      "Zeplyn can take notes during a live mic call; within virtual meeting software like Zoom, Webex, Teams, or Google Meet; or call you for a conference call.",
  },
  {
    title: "Sending the Notetaker",
    element: "#send-notetaker",
    hasDynamicElement: true,
    intro:
      "While Zeplyn can auto-join, you can also send the Notetaker directly to a meeting.",
  },
];

export default tutorialSteps;
