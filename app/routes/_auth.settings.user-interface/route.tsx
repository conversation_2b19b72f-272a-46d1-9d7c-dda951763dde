import { useState } from "react";
import {
  Link,
  LoaderFunctionArgs,
  redirect,
  useLoaderData,
} from "react-router";
import { ArrowLeft, Save } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import SettingTypeTextBlock from "~/routes/_auth.settings.$/components/SettingTypeTextBlock";
import SettingTypeDropdown from "~/routes/_auth.settings.$/components/SettingTypeDropdown";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import { getTheme, saveUiSettingsInCookies } from "~/utils/uiCookies";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // redirect to /settings if flag is not enabled
  if (!isFlagEnabled(request, "EnableUISettings")) {
    return redirect("/settings");
  }

  return {
    theme: getTheme(request.headers.get("Cookie")),
  };
};

const UserImpersonation = () => {
  const { theme: themeCookieValue } = useLoaderData();

  const [theme, setTheme] = useState(themeCookieValue);
  const [showSavedMsg, setShowSavedMsg] = useState(false);

  const handleSubmit = () => {
    if (theme === "") {
      alert("theme is required");
      return;
    }

    const settings = {
      theme,
    };

    saveUiSettingsInCookies(settings);

    setShowSavedMsg(true);

    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings">
            <ArrowLeft />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">User Interface</h2>
      </div>

      <SettingTypeTextBlock
        id="note"
        label="Note"
        details={
          'All settings in this section are "saved locally", and are applied only to your current browser.'
        }
      />

      <SettingTypeDropdown
        id="theme"
        label="Theme"
        value={theme}
        options={[
          { id: "light", label: "Light" },
          { id: "dark", label: "Dark" },
        ]}
        onChange={(_, value) => setTheme(value)}
      />

      <div className="flex items-center">
        <Button onClick={handleSubmit}>
          <Save />
          Save and refresh
        </Button>
        {showSavedMsg && (
          <span className="ml-4 text-sm text-success">
            Changes saved! Refreshing your page..
          </span>
        )}
      </div>
    </div>
  );
};

export default UserImpersonation;
