// API URL: /feapi/notes/save
import { data, LoaderFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const requestData = url.searchParams.get("data") as string;

    const {
      noteId,
      clientName,
      clientId,
      // meetingName,
      // meetingType,
      // attendees,
      // meetingLink,
      // meetingSourceId,
      // scheduledStartTime,
      // scheduledEndTime,
      // scheduledEventUuid,
    } = JSON.parse(requestData);

    // eslint-disable-next-line no-console
    console.log("requestData", noteId, clientName, clientId);

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteApi = new NoteApi(configuration);
    const response = await noteApi.noteEditNote({
      noteId,
      editNoteRequest: {
        client: {
          name: clientName,
          uuid: clientId,
          email: null,
        },
      },
    });

    // eslint-disable-next-line no-console
    console.info("\n\nresponse after saving note", response);

    return { success: true };
  } catch (e) {
    return { success: false };
  }
};
